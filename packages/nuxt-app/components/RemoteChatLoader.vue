<template>
  <div class="remote-chat-loader">
    <!-- 自定义加载指示器 -->
    <div v-if="!iframeReady && !error" class="chat-loading-overlay">
      <div class="loading-spinner" />
      <div class="loading-content">
        <h3>Loading Chat...</h3>
        <p>Preparing your conversation</p>
        <div class="loading-progress">
          <div class="progress-bar" :style="{ width: loadingProgress + '%' }" />
        </div>
      </div>
    </div>

    <!-- iframe 初始隐藏，就绪后显示 -->
    <iframe
      ref="chatIframe"
      :src="chatAppUrl"
      class="chat-iframe"
      :class="{ 'iframe-ready': iframeReady, 'iframe-hidden': !iframeReady }"
      frameborder="0"
      @load="onIframeLoad"
      @error="onIframeError"
    />

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <h2>Error Loading Chat</h2>
      <p>{{ error }}</p>
      <p class="error-url">尝试加载: {{ chatAppUrl }}</p>
      <button class="retry-button" @click="retryLoad">Retry</button>
      <button class="redirect-button" @click="redirectToChat"
        >Open in New Tab</button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  chatType: 'chat' | 'chat2' | 'chat3' | 'chat4'
  characterId?: string
  storyId?: string
}

const props = defineProps<Props>()

const route = useRoute()
const error = ref<string | null>(null)
const chatIframe = ref<HTMLIFrameElement | null>(null)
const iframeReady = ref(false)
const loadingProgress = ref(0)
const loadingTimeout = ref<NodeJS.Timeout | null>(null)

// 构建子应用URL
const chatAppUrl = computed(() => {
  const runtimeConfig = useRuntimeConfig()
  const baseUrl = runtimeConfig.public.csrAppUrl

  if (!baseUrl) {
    console.error(
      '❌ CSR App URL 未配置，请检查 NUXT_PUBLIC_CSR_APP_URL 环境变量',
    )
    return ''
  }

  let path = ''

  switch (props.chatType) {
    case 'chat':
    case 'chat2':
    case 'chat4':
      path = `/${props.chatType}/${props.storyId}/${props.characterId}`
      break
    case 'chat3':
      path = `/${props.chatType}/${props.characterId}/${props.storyId}`
      break
    default:
      path = `/chat/${props.storyId}/${props.characterId}`
  }

  // 不通过URL传递restart参数，改用postMessage避免iframe重新加载
  const fullUrl = `${baseUrl}${path}`

  return fullUrl
})

// iframe加载完成
const onIframeLoad = () => {
  error.value = null
  console.log('📱 iframe 加载完成，等待 CSR-app 路由就绪')

  // 开始加载进度动画
  startLoadingProgress()

  // 设置超时机制，防止永久等待
  loadingTimeout.value = setTimeout(() => {
    console.warn('⚠️ 等待 CSR-app 就绪超时，强制显示 iframe')
    showIframe()

    // 发送超时事件用于监控
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'iframe_load_timeout', {
        event_category: 'performance',
        event_label: 'chat_iframe_timeout',
        value: 3000,
      })
    }
  }, 3000) // 3秒超时

  // 如果需要restart，通过postMessage通知CSR应用
  const shouldRestart = route.query.restart === '1'

  if (shouldRestart) {
    // 延迟发送postMessage，确保iframe完全加载
    setTimeout(() => {
      const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        const runtimeConfig = useRuntimeConfig()
        const message = {
          type: 'RESTART_GAME',
          payload: { restart: true },
          timestamp: Date.now(),
          source: 'nuxt-app',
        }

        iframe.contentWindow.postMessage(
          message,
          runtimeConfig.public.csrAppUrl,
        )
      }
    }, 1000)

    // 立即清除URL中的restart参数，避免刷新时重复
    nextTick(() => {
      const currentQuery = { ...route.query }
      delete currentQuery.restart

      navigateTo({ query: currentQuery }, { replace: true })
    })
  }
}

// 开始加载进度动画
const startLoadingProgress = () => {
  loadingProgress.value = 20
  const progressInterval = setInterval(() => {
    if (loadingProgress.value < 80) {
      loadingProgress.value += Math.random() * 10 + 5
    } else {
      clearInterval(progressInterval)
    }
  }, 200)
}

// 显示 iframe
const showIframe = () => {
  if (iframeReady.value) return // 防止重复调用

  console.log('✅ 显示 iframe')
  iframeReady.value = true
  loadingProgress.value = 100

  // 清除超时定时器
  if (loadingTimeout.value) {
    clearTimeout(loadingTimeout.value)
    loadingTimeout.value = null
  }

  // 发送成功显示事件用于监控
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'iframe_show_success', {
      event_category: 'performance',
      event_label: 'chat_iframe_ready',
    })
  }
}

// iframe加载错误
const onIframeError = () => {
  error.value =
    'Failed to load chat application. Please check if the chat service is running.'

  // 清除超时定时器
  if (loadingTimeout.value) {
    clearTimeout(loadingTimeout.value)
    loadingTimeout.value = null
  }

  // 发送错误事件用于监控
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'iframe_load_error', {
      event_category: 'error',
      event_label: 'chat_iframe_error',
      value: 1,
    })
  }

  console.error('❌ iframe 加载失败:', chatAppUrl.value)
}

// 重试加载
const retryLoad = () => {
  error.value = null
  iframeReady.value = false
  loadingProgress.value = 0

  // 清除超时定时器
  if (loadingTimeout.value) {
    clearTimeout(loadingTimeout.value)
    loadingTimeout.value = null
  }

  // 重新加载iframe
  const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
  if (iframe) {
    const currentSrc = iframe.src
    iframe.src = ''
    setTimeout(() => {
      iframe.src = currentSrc
    }, 100)
  }
}

// 重定向到新标签页
const redirectToChat = () => {
  window.open(chatAppUrl.value, '_blank')
}

// 处理来自CSR应用的消息
const handleMessage = (event: MessageEvent) => {
  // 确保消息来自我们的CSR应用
  const runtimeConfig = useRuntimeConfig()
  const csrAppUrl = runtimeConfig.public.csrAppUrl
  if (!csrAppUrl || event.origin !== csrAppUrl) {
    return
  }

  const { type, payload } = event.data

  switch (type) {
    case 'CHAT_ROUTE_READY':
      // CSR-app 的 chat 路由已就绪，可以显示 iframe
      console.log('🎯 收到 CSR-app 路由就绪消息')
      showIframe()
      break
    case 'NAVIGATE_TO_HOME':
      // 导航到主应用首页
      navigateTo('/')
      break
    case 'NAVIGATE_TO_STORIES':
      // 导航到故事列表
      navigateTo('/stories')
      break
    case 'NAVIGATE_TO_STORY':
      // 导航到特定故事
      if (payload?.storyId) {
        navigateTo(`/story/${payload.storyId}`)
      }
      break
    case 'NAVIGATE_TO_NUXT_PAGE':
      // 导航到Nuxt中的其他页面
      if (payload?.path) {
        navigateTo(payload.path)
      }
      break
    case 'GO_BACK':
      // 返回上一页
      if (import.meta.client) {
        window.history.back()
      }
      break
    case 'SYNC_AUTH_STATE':
      // 同步认证状态
      handleAuthStateSync(payload)
      break
    case 'SYNC_USER_STATE':
      // 同步用户状态
      handleUserStateSync(payload)
      break
    case 'REQUEST_STATE':
      // CSR应用请求状态
      sendStateToCSR()
      break
    case 'REQUEST_STORY_DATA':
      // CSR应用请求故事数据
      handleStoryDataRequest(payload)
      break
    case 'SYNC_STORY_STATE':
      // 同步故事状态
      handleStoryStateSync(payload)
      break
    case 'SOCIAL_LOGIN_REQUEST':
      // 处理来自CSR应用的社交登录请求
      handleSocialLoginRequest(payload)
      break
    case 'LOADING_PROGRESS':
      // 接收CSR应用的加载进度（现在不需要处理）
      break
    case 'CSR_APP_READY':
      // CSR应用已就绪（现在不需要处理）
      break
    case 'CSR_ROUTE_READY':
      // CSR应用路由已就绪（现在不需要处理）
      break
    case 'PAYMENT_REDIRECT':
      // 处理来自CSR应用的支付重定向请求
      handlePaymentRedirect(payload)
      break
    default:
      console.warn('未知的消息类型:', type)
  }
}

// 状态同步处理函数
const handleAuthStateSync = (authData: any) => {
  console.log('🔐 主应用: 接收到认证状态同步', authData)

  // 这里可以更新主应用的认证状态
  // 例如：更新token到localStorage或store
  if (authData.token) {
    localStorage.setItem('token', authData.token)
  }
  if (authData.refreshToken) {
    localStorage.setItem('refreshToken', authData.refreshToken)
  }
  if (authData.userId) {
    localStorage.setItem('userId', authData.userId)
  }
}

const handleUserStateSync = (userData: any) => {
  console.log('👤 主应用: 接收到用户状态同步', userData)

  // 这里可以更新主应用的用户状态
  // 例如：更新用户信息到store
  if (userData.user) {
    localStorage.setItem('userInfo', JSON.stringify(userData.user))
  }
  if (userData.language) {
    localStorage.setItem('language', userData.language)
  }
  if (userData.theme) {
    localStorage.setItem('theme', userData.theme)
  }
}

// 处理来自CSR应用的社交登录请求
const handleSocialLoginRequest = async (payload: any) => {
  console.log('🔐 主应用: 接收到社交登录请求', payload)

  try {
    const { type, social_redirect_url, app_redirect_url } = payload

    // 存储重定向URL
    if (import.meta.client && app_redirect_url) {
      sessionStorage.setItem('login_redirect', app_redirect_url)
    }

    // 调用主应用的社交登录API
    const config = useRuntimeConfig()
    const response = await $fetch(
      `${config.public.apiBase}/api/v1/social-login.get-url`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          login_type: type,
          redirect_url: social_redirect_url,
        },
      },
    )

    if (response.code !== '0' || !response.data?.url) {
      throw new Error('Failed to get social login URL')
    }

    const loginUrl = response.data.url
    if (!loginUrl.startsWith('http')) {
      throw new Error('Invalid login URL')
    }

    // 在主应用中跳转到社交登录页面
    window.location.href = loginUrl
  } catch (error: any) {
    console.error('社交登录请求处理失败:', error)
    // 可以通过postMessage通知CSR应用登录失败
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      const runtimeConfig = useRuntimeConfig()
      iframe.contentWindow.postMessage(
        {
          type: 'SOCIAL_LOGIN_ERROR',
          error: error.message || 'Social login failed',
        },
        runtimeConfig.public.csrAppUrl,
      )
    }
  }
}

// 处理故事数据请求
const handleStoryDataRequest = async (payload: any) => {
  console.log('📚 主应用: 收到故事数据请求', payload)

  const { storyId, actorId } = payload

  try {
    // 使用 Nuxt 的 $fetch 来获取故事数据
    const config = useRuntimeConfig()
    const [storyResponse, actorResponse] = await Promise.all([
      $fetch(`${config.public.apiBase}/api/v1/stories/${storyId}`),
      $fetch(`${config.public.apiBase}/api/v1/stories/${storyId}/actors`),
    ])

    let story = null
    let actor = null

    if (storyResponse.code === '0') {
      story = storyResponse.data.story
    }

    if (actorResponse.code === '0') {
      actor = actorResponse.data.actors?.find((a: any) => a.id === actorId)
    }

    // 发送数据到CSR应用
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage(
        {
          type: 'STORY_DATA_RESPONSE',
          payload: { story, actor },
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
      console.log('📚 主应用: 已发送故事数据到CSR应用', { story, actor })
    }
  } catch (error) {
    console.error('获取故事数据失败:', error)
    // 发送错误响应
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      const config = useRuntimeConfig()
      iframe.contentWindow.postMessage(
        {
          type: 'STORY_DATA_ERROR',
          error: error.message || 'Failed to fetch story data',
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
    }
  }
}

// 处理故事状态同步
const handleStoryStateSync = (payload: any) => {
  console.log('📚 主应用: 收到故事状态同步', payload)

  // 这里可以将故事状态保存到主应用的状态管理中
  // 例如：存储到 localStorage 或 store
  if (payload.currentStory) {
    localStorage.setItem('currentStory', JSON.stringify(payload.currentStory))
  }
  if (payload.currentActor) {
    localStorage.setItem('currentActor', JSON.stringify(payload.currentActor))
  }
}

// 处理来自CSR应用的支付重定向请求
const handlePaymentRedirect = async (payload: any) => {
  console.log('💳 主应用: 收到支付重定向请求', payload)

  try {
    const { provider, redirectUrl, sessionId, useStripeSDK, stripePublicKey } =
      payload

    if (provider === 'stripe' && useStripeSDK && sessionId && stripePublicKey) {
      // 使用Stripe SDK处理支付
      try {
        // 动态导入 Stripe SDK
        const { loadStripe } = await import('@stripe/stripe-js')
        const stripe = await loadStripe(stripePublicKey)

        if (!stripe) {
          throw new Error('Failed to load Stripe SDK')
        }

        // 使用官方 SDK 重定向
        const { error } = await stripe.redirectToCheckout({
          sessionId: sessionId,
        })

        if (error) {
          throw new Error(error.message || 'Stripe redirect failed')
        }
      } catch (error) {
        console.error(
          '💳 主应用: 使用Stripe SDK重定向失败，降级到直接重定向',
          error,
        )
        // 降级到直接 URL 重定向
        window.location.href = `https://checkout.stripe.com/pay/${sessionId}`
      }
    } else if (redirectUrl) {
      // 其他支付方式使用直接重定向
      window.location.href = redirectUrl
    } else {
      throw new Error('No valid payment redirect information provided')
    }
  } catch (error: any) {
    console.error('💳 主应用: 支付重定向失败', error)

    // 通知CSR应用支付重定向失败
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      const config = useRuntimeConfig()
      iframe.contentWindow.postMessage(
        {
          type: 'PAYMENT_REDIRECT_ERROR',
          error: error.message || 'Payment redirect failed',
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
    }
  }
}

const sendStateToCSR = () => {
  console.log('📤 主应用: 发送状态到CSR应用')

  // 获取配置
  const config = useRuntimeConfig()

  // 获取主应用的状态
  const authState = {
    token: localStorage.getItem('token'),
    refreshToken: localStorage.getItem('refreshToken'),
    userId: localStorage.getItem('userId'),
  }

  const userState = {
    user: localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo')!)
      : null,
    language: localStorage.getItem('language'),
    theme: localStorage.getItem('theme'),
  }

  const storyState = {
    currentStory: localStorage.getItem('currentStory')
      ? JSON.parse(localStorage.getItem('currentStory')!)
      : null,
    currentActor: localStorage.getItem('currentActor')
      ? JSON.parse(localStorage.getItem('currentActor')!)
      : null,
  }

  // 获取当前页面信息
  const pageState = {
    currentUrl: window.location.href,
    pathname: window.location.pathname,
    search: window.location.search,
    hash: window.location.hash,
  }

  console.log('📤 主应用: 准备发送的状态', {
    auth: authState,
    user: userState,
    story: storyState,
    page: pageState,
    targetUrl: config.public.csrAppUrl,
  })

  // 发送状态到CSR应用
  const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
  if (iframe && iframe.contentWindow) {
    console.log('📤 主应用: 找到iframe，发送消息')
    iframe.contentWindow.postMessage(
      {
        type: 'STATE_SYNC',
        payload: {
          auth: authState,
          user: userState,
          story: storyState,
          page: pageState,
        },
        timestamp: Date.now(),
        source: 'nuxt-app',
      },
      config.public.csrAppUrl,
    )
  } else {
    console.warn('❌ 主应用: 未找到iframe或iframe未加载完成')
  }
}

// 组件挂载时的事件监听
onMounted(() => {
  // 监听来自CSR应用的消息
  if (import.meta.client) {
    window.addEventListener('message', handleMessage)
  }

  // 延迟发送初始状态到CSR应用
  setTimeout(() => {
    sendStateToCSR()
  }, 1000)

  // 监听登录状态变化，当从第三方登录回来时重新同步状态
  if (import.meta.client) {
    const checkAuthStateChange = () => {
      const needSync = localStorage.getItem('needSyncToCSR')
      const currentToken = localStorage.getItem('token')
      const currentUserId = localStorage.getItem('userId')

      // 如果有同步标记或检测到新的登录状态，重新发送状态到CSR应用
      if (needSync === 'true' || (currentToken && currentUserId)) {
        console.log('🔄 检测到需要同步状态到CSR应用')

        // 清除同步标记
        if (needSync === 'true') {
          localStorage.removeItem('needSyncToCSR')
        }

        setTimeout(() => {
          sendStateToCSR()
        }, 500)
      }
    }

    // 立即检查一次
    checkAuthStateChange()

    // 页面获得焦点时检查登录状态（从第三方登录回来时会触发）
    window.addEventListener('focus', checkAuthStateChange)

    // 页面可见性变化时检查登录状态
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        checkAuthStateChange()
      }
    })

    // 定期检查是否需要同步（作为备用机制）
    const syncCheckInterval = setInterval(() => {
      const needSync = localStorage.getItem('needSyncToCSR')
      if (needSync === 'true') {
        console.log('🔄 定期检查发现需要同步状态')
        checkAuthStateChange()
      }
    }, 2000)

    // 组件卸载时清理定时器
    onUnmounted(() => {
      clearInterval(syncCheckInterval)
    })
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('message', handleMessage)
  }

  // 清理超时定时器
  if (loadingTimeout.value) {
    clearTimeout(loadingTimeout.value)
    loadingTimeout.value = null
  }
})

// 监听props变化，更新URL
watch(
  [() => props.chatType, () => props.storyId, () => props.characterId],
  () => {
    error.value = null
  },
)
</script>

<style scoped>
.remote-chat-loader {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
}

.chat-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #000;
  transition: opacity 0.3s ease-in-out;
}

.chat-iframe.iframe-hidden {
  opacity: 0;
  pointer-events: none;
}

.chat-iframe.iframe-ready {
  opacity: 1;
  pointer-events: auto;
}

.chat-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  z-index: 5;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 2rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-content {
  text-align: center;
  max-width: 300px;
}

.loading-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.loading-content p {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: white;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  z-index: 10;
}

.error-overlay h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error-overlay p {
  color: #ccc;
  text-align: center;
  margin-bottom: 1.5rem;
  max-width: 400px;
}

.retry-button,
.redirect-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0.5rem;
  transition: background-color 0.2s;
}

.retry-button:hover,
.redirect-button:hover {
  background: #2980b9;
}

.redirect-button {
  background: #27ae60;
}

.redirect-button:hover {
  background: #229954;
}
</style>
